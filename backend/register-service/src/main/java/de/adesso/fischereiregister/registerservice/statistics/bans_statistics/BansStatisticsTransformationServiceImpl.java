package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BansStatisticsTransformationServiceImpl implements BansStatisticsTransformationService {

    @Override
    public List<BansStatistics> transformToBansStatistics(List<BansStatisticsView> statisticsViews, List<Integer> yearsToQuery) {
        try {
            // Group statistics by year
            Map<Integer, List<BansStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(BansStatisticsView::getYear));

            List<BansStatistics> result = new ArrayList<>();

            // For each requested year, create a BansStatistics object
            for (Integer year : yearsToQuery) {
                List<BansStatisticsView> yearStats = statsByYear.get(year);

                int issuedCount = 0;
                int expiredCount = 0;
                int startedCount = 0;

                if (yearStats != null && !yearStats.isEmpty()) {
                    // Sum issued, expired, and started counts for the year
                    issuedCount = yearStats.stream()
                            .mapToInt(BansStatisticsView::getIssuedCount)
                            .sum();

                    expiredCount = yearStats.stream()
                            .mapToInt(BansStatisticsView::getExpiredCount)
                            .sum();

                    startedCount = yearStats.stream()
                            .mapToInt(BansStatisticsView::getStartedCount)
                            .sum();
                }

                // Create data entry with issued, expired, and started counts (zero if no data)
                BansStatisticsData data = new BansStatisticsData(issuedCount, startedCount, expiredCount);

                // Create BansStatistics with the year and data entry
                BansStatistics bansStatistics = new BansStatistics(year, data);
                result.add(bansStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(BansStatistics::year).reversed());

            return result;
        } catch (Exception e) {
            log.error("Error transforming ban statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform ban statistics", e);
        }
    }
}
